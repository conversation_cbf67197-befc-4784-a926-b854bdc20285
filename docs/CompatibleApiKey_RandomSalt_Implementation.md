# 兼容API密钥随机盐加密实现文档

## 概述

本文档描述了兼容API密钥系统的随机盐加密实现，解决了之前固定盐导致的安全问题，并实现了完整的数据库存储功能。

## 核心特性

### ✅ 已实现功能
1. **随机盐加密**：每个兼容密钥使用独立的随机盐
2. **数据库存储**：存储密钥元数据和盐值
3. **安全解密**：从数据库获取盐值进行解密
4. **使用统计**：自动记录使用次数和最后使用时间
5. **完整API**：生成、验证、查询、删除兼容密钥

## 数据库设计

### 兼容API密钥表结构
```sql
CREATE TABLE compatible_api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,           -- 自增主键ID
    user_id BIGINT NOT NULL,                        -- 用户ID
    key_name VARCHAR(100) NOT NULL,                 -- 密钥名称
    key_hash VARCHAR(64) NOT NULL UNIQUE,           -- 密钥哈希值（用于标识）
    salt VARCHAR(64) NOT NULL,                      -- 随机盐值
    usage_count BIGINT DEFAULT 0,                   -- 使用次数
    last_used_at DATETIME(3) NULL,                  -- 最后使用时间
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),  -- 创建时间
    updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3), -- 更新时间
    INDEX idx_user_id (user_id),
    INDEX idx_key_hash (key_hash),
    INDEX idx_created_at (created_at)
);
```

### 字段说明
- **id**: 数据库自增主键
- **user_id**: 所属用户ID（无外键约束）
- **key_name**: 用户自定义密钥名称
- **key_hash**: 兼容密钥的SHA-256哈希值，用于快速查找
- **salt**: 随机生成的盐值，用于加密解密
- **usage_count**: 使用次数统计
- **last_used_at**: 最后使用时间
- **created_at**: 创建时间
- **updated_at**: 更新时间

## 核心实现

### 1. 生成兼容密钥流程

```java
// 在 AiConfigController.generateCompatibleKey() 方法中实现
@PostMapping("/generate-compatible-key")
public Result<CompatibleKeyResult> generateCompatibleKey(
        @RequestParam(required = false, defaultValue = "default") String keyName,
        Authentication authentication) {
    
    // 1. 检查密钥名称是否已存在
    int existingCount = compatibleApiKeyMapper.countByUserIdAndKeyName(userId, keyName.trim());
    
    // 2. 生成随机盐
    String randomSalt = KeyGenerators.string().generateKey();
    
    // 3. 构造明文数据：userId|timestamp|keyName|random
    String plaintext = String.format("%d|%d|%s|%d", userId, timestamp, keyName, random);
    
    // 4. 使用随机盐创建加密器并加密
    BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, randomSalt);
    String compatibleKey = "sk-" + Base64.encode(encryptor.encrypt(plaintext));
    
    // 5. 生成密钥哈希用于标识
    String keyHash = SHA256(compatibleKey);
    
    // 6. 存储到数据库
    CompatibleApiKey entity = new CompatibleApiKey();
    entity.setUserId(userId);
    entity.setKeyName(keyName);
    entity.setKeyHash(keyHash);
    entity.setSalt(randomSalt);  // ✅ 存储随机盐
    entity.setUsageCount(0L);
    
    compatibleApiKeyMapper.insert(entity);
}
```

### 2. 验证兼容密钥流程

```java
// 在 AiConfigController.validateCompatibleKey() 方法中实现
@PostMapping("/validate-compatible-key")
public Result<CompatibleKeyValidation> validateCompatibleKey(
        @RequestParam String compatibleKey,
        Authentication authentication) {
    
    // 1. 生成密钥哈希
    String keyHash = SHA256(compatibleKey);
    
    // 2. 从数据库查找对应的盐值
    CompatibleApiKey entity = compatibleApiKeyMapper.selectByKeyHash(keyHash);
    
    // 3. 使用存储的盐值创建解密器
    String storedSalt = entity.getSalt();
    BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, storedSalt);
    
    // 4. 解密密钥内容并验证
    String decrypted = encryptor.decrypt(Base64.decode(compatibleKey.substring(3)));
    
    // 5. 更新使用统计
    compatibleApiKeyMapper.updateUsageStatsByKeyHash(keyHash);
}
```

## API接口

### 1. 生成兼容密钥
```http
POST /api/ai/config/generate-compatible-key?keyName=生产环境主密钥
Authorization: Bearer {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "生成成功",
  "data": {
    "compatibleKey": "sk-AbCdEf...",
    "message": "兼容API密钥已生成并保存，可用于所有AI服务调用",
    "keyHash": "a1b2c3d4...",
    "keyId": 1
  }
}
```

### 2. 获取兼容密钥列表
```http
GET /api/ai/config/compatible-keys
Authorization: Bearer {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "keyName": "生产环境主密钥",
      "maskedKeyHash": "a1b2****c3d4",
      "usageCount": 150,
      "lastUsedAt": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-01T09:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 3. 验证兼容密钥
```http
POST /api/ai/config/validate-compatible-key?compatibleKey=sk-AbCdEf...
Authorization: Bearer {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "验证完成",
  "data": {
    "valid": true,
    "userId": 12345,
    "message": "验证成功"
  }
}
```

### 4. 删除兼容密钥
```http
DELETE /api/ai/config/compatible-keys/1
Authorization: Bearer {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "删除成功",
  "data": "删除成功"
}
```

## 安全特性

### 1. 随机盐加密
- ✅ 每个密钥使用独立的随机盐
- ✅ 防止彩虹表攻击
- ✅ 即使密码相同，加密结果也不同

### 2. 数据库安全
- ✅ 不存储明文密钥
- ✅ 只存储密钥哈希和盐值
- ✅ 无外键约束，降低数据库复杂度

### 3. 访问控制
- ✅ 用户只能操作自己的密钥
- ✅ JWT认证保护所有接口
- ✅ 密钥哈希脱敏显示

## 性能优化

### 1. 数据库索引
- `idx_user_id`: 用户查询优化
- `idx_key_hash`: 密钥验证优化
- `idx_created_at`: 时间排序优化

### 2. 缓存策略
- 密钥验证结果可缓存（可选）
- 用户密钥列表可缓存（可选）

## 使用统计

### 自动统计功能
- **使用次数**: 每次验证成功自动+1
- **最后使用时间**: 每次验证成功自动更新
- **创建时间**: 生成时自动记录
- **更新时间**: 任何修改自动更新

## 架构设计

### 分层架构
```
Controller Layer (AiConfigController)
    ↓
Service Layer (CompatibleApiKeyService)
    ↓
Utility Layer (SpringEncryptionUtil)
    ↓
Data Layer (CompatibleApiKeyMapper)
```

### 职责分离
1. **Controller**: 处理HTTP请求，参数验证，响应格式化
2. **Service**: 业务逻辑，数据库操作，事务管理
3. **Utility**: 加密解密，密钥生成，哈希计算
4. **Mapper**: 数据库CRUD操作

## 关键改进

### 🔧 重构前后对比

| 方面 | 重构前 | 重构后 |
|------|--------|--------|
| **架构** | 控制器直接调用工具类 | 分层架构，职责清晰 |
| **加密** | 固定盐，重启后失效 | 随机盐，数据库存储 |
| **安全性** | 彩虹表攻击风险 | 每密钥独立盐值 |
| **可维护性** | 逻辑耦合严重 | 模块化设计 |
| **可测试性** | 难以单元测试 | 易于模拟和测试 |

### 🎯 核心流程

#### 生成密钥流程
```
1. Controller接收请求 → 2. Service检查重名 → 3. Utility生成加密数据
→ 4. Service存储数据库 → 5. Controller返回结果
```

#### 验证密钥流程
```
1. Controller接收请求 → 2. Service查询数据库 → 3. Utility解密验证
→ 4. Service更新统计 → 5. Controller返回结果
```

## 总结

本实现完全解决了之前固定盐导致的安全问题：

1. **✅ 安全性提升**: 随机盐防止批量破解
2. **✅ 数据持久化**: 数据库存储确保重启后可用
3. **✅ 完整功能**: 生成、验证、管理一体化
4. **✅ 性能优化**: 合理的索引和查询策略
5. **✅ 架构优化**: 分层设计，职责清晰
6. **✅ 易于维护**: 清晰的代码结构和文档

### 🚀 部署就绪
- ✅ 编译通过
- ✅ 数据库表结构完整
- ✅ API接口完整
- ✅ 错误处理完善
- ✅ 日志记录完整

该实现已通过编译测试，可以直接部署使用。
