package com.example.pure.util;

import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import com.example.pure.model.entity.CompatibleApiKey;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 测试SpringEncryptionUtil的随机salt数据库存储功能
 */
public class SpringEncryptionUtilSaltTest {

    @Test
    public void testRandomSaltWithDatabaseStorage() {
        // 模拟SnowflakeIdGenerator
        SnowflakeIdGenerator mockGenerator = new SnowflakeIdGenerator(1, 1);

        // 模拟CompatibleApiKeyMapper
        CompatibleApiKeyMapper mockMapper = Mockito.mock(CompatibleApiKeyMapper.class);

        // 创建SpringEncryptionUtil实例
        SpringEncryptionUtil util = new SpringEncryptionUtil(mockGenerator);

        // 设置配置
        ReflectionTestUtils.setField(util, "encryptionPassword", "testPassword123");
        ReflectionTestUtils.setField(util, "encryptionSalt", "64656661756c7453616c74343536");
        ReflectionTestUtils.setField(util, "compatibleApiKeyMapper", mockMapper);

        // 初始化
        util.init();

        // 模拟数据库操作
        when(mockMapper.countByUserIdAndKeyName(anyLong(), anyString())).thenReturn(0);
        when(mockMapper.insert(any(CompatibleApiKey.class))).thenReturn(1);

        // 模拟查询返回的实体
        CompatibleApiKey mockEntity = new CompatibleApiKey();
        mockEntity.setId(1L);
        mockEntity.setUserId(12345L);
        mockEntity.setKeyName("testKey");
        mockEntity.setSalt("mockSalt123");
        mockEntity.setUsageCount(0L);
        mockEntity.setCreatedAt(Instant.now());

        when(mockMapper.selectByKeyHash(anyString())).thenReturn(mockEntity);
        when(mockMapper.updateUsageStatsByKeyHash(anyString())).thenReturn(1);

        // 生成兼容密钥
        SpringEncryptionUtil.CompatibleKeyResult result = util.generateCompatibleApiKey(12345L, "testKey");
        String compatibleKey = result.getCompatibleKey();

        // 验证生成结果
        assertNotNull(compatibleKey);
        assertTrue(compatibleKey.startsWith("sk-"));
        assertNotNull(result.getSecurityHash());

        // 验证数据库操作被调用
        verify(mockMapper, times(1)).countByUserIdAndKeyName(12345L, "testKey");
        verify(mockMapper, times(1)).insert(any(CompatibleApiKey.class));

        System.out.println("生成的兼容密钥: " + compatibleKey);
        System.out.println("密钥哈希: " + result.getSecurityHash());

        // 测试解析功能
        SpringEncryptionUtil.ParseResultV2 parseResult = util.parseCompatibleApiKey(compatibleKey);

        // 验证解析结果
        assertTrue(parseResult.isValid(), "应该能够成功解析密钥");
        assertEquals(12345L, parseResult.getUserId());
        assertEquals("testKey", parseResult.getKeyName());

        // 验证数据库查询和更新被调用
        verify(mockMapper, times(1)).selectByKeyHash(anyString());
        verify(mockMapper, times(1)).updateUsageStatsByKeyHash(anyString());

        System.out.println("解析结果: " + parseResult.getMessage());
        System.out.println("是否成功: " + parseResult.isValid());
    }

    @Test
    public void testRandomSaltGeneration() {
        SnowflakeIdGenerator mockGenerator = new SnowflakeIdGenerator(1, 1);
        SpringEncryptionUtil util = new SpringEncryptionUtil(mockGenerator);

        // 模拟CompatibleApiKeyMapper
        CompatibleApiKeyMapper mockMapper = Mockito.mock(CompatibleApiKeyMapper.class);
        ReflectionTestUtils.setField(util, "compatibleApiKeyMapper", mockMapper);
        ReflectionTestUtils.setField(util, "encryptionPassword", "testPassword123");
        ReflectionTestUtils.setField(util, "encryptionSalt", "64656661756c7453616c74343536");

        util.init();

        // 模拟数据库操作
        when(mockMapper.countByUserIdAndKeyName(anyLong(), anyString())).thenReturn(0);
        when(mockMapper.insert(any(CompatibleApiKey.class))).thenReturn(1);

        // 生成多个兼容密钥，验证每次使用的salt都不同
        SpringEncryptionUtil.CompatibleKeyResult result1 = util.generateCompatibleApiKey(12345L, "testKey1");
        SpringEncryptionUtil.CompatibleKeyResult result2 = util.generateCompatibleApiKey(12345L, "testKey2");
        SpringEncryptionUtil.CompatibleKeyResult result3 = util.generateCompatibleApiKey(12345L, "testKey3");

        System.out.println("密钥1哈希: " + result1.getSecurityHash());
        System.out.println("密钥2哈希: " + result2.getSecurityHash());
        System.out.println("密钥3哈希: " + result3.getSecurityHash());

        // ✅ 新实现：每次生成的密钥哈希应该都不同（因为使用了随机salt）
        assertNotEquals(result1.getSecurityHash(), result2.getSecurityHash(), "使用随机salt：每次生成的密钥哈希应该不同");
        assertNotEquals(result2.getSecurityHash(), result3.getSecurityHash(), "使用随机salt：每次生成的密钥哈希应该不同");
        assertNotEquals(result1.getSecurityHash(), result3.getSecurityHash(), "使用随机salt：每次生成的密钥哈希应该不同");

        // 验证密钥格式正确
        assertTrue(result1.getCompatibleKey().startsWith("sk-"));
        assertTrue(result2.getCompatibleKey().startsWith("sk-"));
        assertTrue(result3.getCompatibleKey().startsWith("sk-"));

        // 验证数据库插入被调用了3次
        verify(mockMapper, times(3)).insert(any(CompatibleApiKey.class));
    }
}
