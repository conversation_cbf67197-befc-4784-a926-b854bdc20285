package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import com.example.pure.model.dto.response.openai.ApiKeyTestResult;
import com.example.pure.model.dto.request.openai.AddApiKeyRequest;
import com.example.pure.model.dto.response.openai.ApiKeyDto;
import com.example.pure.model.dto.request.openai.UpdateApiKeyRequest;
import com.example.pure.model.dto.response.openai.UserConfigDto;
import com.example.pure.model.entity.CompatibleApiKey;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.openai.SimpleCompatibleKeyService;
import com.example.pure.util.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.BytesEncryptor;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.keygen.KeyGenerators;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.Base64;
import java.util.concurrent.ThreadLocalRandom;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI配置管理控制器
 * <p>
 * 提供用户AI配置和API密钥的管理功能
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/ai/config")
@RequiredArgsConstructor
@Tag(name = "AI配置管理", description = "用户AI配置和API密钥管理接口")
public class AiConfigController {

    private final AiConfigService aiConfigService;
    private final LoadBalancerService loadBalancerService;
    private final SimpleCompatibleKeyService simpleCompatibleKeyService;

    @Autowired
    private CompatibleApiKeyMapper compatibleApiKeyMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword;

    // ========================
    // 用户AI配置管理
    // ========================

    /**
     * 获取用户AI配置
     */
    @GetMapping("/user")
    @Operation(summary = "获取用户AI配置", description = "获取当前用户的AI配置信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<UserConfigDto> getUserConfig(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = aiConfigService.getUserConfig(userId);
            UserConfigDto dto = convertToUserConfigDto(config);

            log.debug("获取用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("获取成功", dto);
        } catch (Exception e) {
            log.error("获取用户AI配置失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }
    /**
     * 更新用户AI配置
     */
    @PutMapping("/user")
    @Operation(summary = "更新用户AI配置", description = "更新当前用户的AI配置信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<UserConfigDto> updateUserConfig(
            @Valid @RequestBody UserConfigDto configDto,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = convertToUserAiConfig(configDto);

            UserAiConfig updatedConfig = aiConfigService.updateUserConfig(userId, config);
            UserConfigDto dto = convertToUserConfigDto(updatedConfig);

            log.info("更新用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("更新成功", dto);
        } catch (Exception e) {
            log.error("更新用户AI配置失败", e);
            return Result.errorTyped(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户AI配置
     */
    @PostMapping("/user/reset")
    @Operation(summary = "重置用户AI配置", description = "将用户AI配置重置为默认值")
    @ApiResponse(responseCode = "200", description = "重置成功")
    public Result<UserConfigDto> resetUserConfig(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            UserAiConfig config = aiConfigService.resetUserConfig(userId);
            UserConfigDto dto = convertToUserConfigDto(config);

            log.info("重置用户AI配置成功 - 用户ID: {}", userId);
            return Result.success("重置成功", dto);
        } catch (Exception e) {
            log.error("重置用户AI配置失败", e);
            return Result.errorTyped(500, "重置失败: " + e.getMessage());
        }
    }

    // ========================
    // API密钥管理
    // ========================

    /**
     * 获取用户的所有API密钥
     */
    @GetMapping("/api-keys")
    @Operation(summary = "获取API密钥列表", description = "获取当前用户的所有API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeys(userId);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户API密钥列表成功 - 用户ID: {}, 数量: {}", userId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据提供商获取API密钥
     */
    @GetMapping("/api-keys/{provider}")
    @Operation(summary = "根据提供商获取API密钥", description = "获取指定提供商的API密钥列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<ApiKeyDto>> getUserApiKeysByProvider(
            @PathVariable UserApiKey.ProviderType provider,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<UserApiKey> apiKeys = aiConfigService.getUserApiKeysByProvider(userId, provider);
            List<ApiKeyDto> dtos = apiKeys.stream()
                    .map(this::convertToApiKeyDto)
                    .collect(Collectors.toList());

            log.debug("获取用户指定提供商API密钥成功 - 用户ID: {}, 提供商: {}, 数量: {}",
                    userId, provider, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户指定提供商API密钥失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 添加API密钥
     */
    @PostMapping("/api-keys")
    @Operation(summary = "添加API密钥", description = "为当前用户添加新的API密钥")
    @ApiResponse(responseCode = "200", description = "添加成功")
    public Result<ApiKeyDto> addApiKey(
            @Valid @RequestBody AddApiKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            UserApiKey apiKey = aiConfigService.addApiKey(
                    userId,
                    request.getProvider(),
                    request.getKeyName(),
                    request.getApiKey(),
                    request.getPriority()
            );

            ApiKeyDto dto = convertToApiKeyDto(apiKey);

            log.info("添加API密钥成功 - 用户ID: {}, 密钥ID: {}, 提供商: {}",
                    userId, apiKey.getId(), request.getProvider());
            return Result.success("添加成功", dto);
        } catch (Exception e) {
            log.error("添加API密钥失败", e);
            return Result.errorTyped(500, "添加失败: " + e.getMessage());
        }
    }

    /**
     * 更新API密钥
     */
    @PutMapping("/api-keys/{keyId}")
    @Operation(summary = "更新API密钥", description = "更新指定的API密钥信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<ApiKeyDto> updateApiKey(
            @PathVariable @NotNull Long keyId,
            @Valid @RequestBody UpdateApiKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            UserApiKey apiKey = aiConfigService.updateApiKey(
                    userId,
                    keyId,
                    request.getKeyName(),
                    request.getApiKey(),
                    request.getPriority(),
                    request.getIsActive()
            );

            ApiKeyDto dto = convertToApiKeyDto(apiKey);

            log.info("更新API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
            return Result.success("更新成功", dto);
        } catch (Exception e) {
            log.error("更新API密钥失败", e);
            return Result.errorTyped(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除API密钥
     */
    @DeleteMapping("/api-keys/{keyId}")
    @Operation(summary = "删除API密钥", description = "删除指定的API密钥")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteApiKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            boolean deleted = aiConfigService.deleteApiKey(userId, keyId);

            if (deleted) {
                log.info("删除API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("删除成功", "删除成功");
            } else {
                return Result.errorTyped(404, "API密钥不存在");
            }
        } catch (Exception e) {
            log.error("删除API密钥失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 测试API密钥
     */
    @PostMapping("/api-keys/{keyId}/test")
    @Operation(summary = "测试API密钥", description = "测试指定API密钥的有效性")
    @ApiResponse(responseCode = "200", description = "测试完成")
    public Result<ApiKeyTestResult> testApiKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            ApiKeyTestResult result = aiConfigService.testApiKey(userId, keyId);

            log.info("测试API密钥完成 - 用户ID: {}, 密钥ID: {}, 结果: {}",
                    userId, keyId, result.isValid());
            return Result.success("测试完成", result);
        } catch (Exception e) {
            log.error("测试API密钥失败", e);
            return Result.errorTyped(500, "测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取API密钥负载统计
     */
    @GetMapping("/api-keys/{keyId}/stats")
    @Operation(summary = "获取API密钥负载统计", description = "获取指定API密钥的负载统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<LoadBalancerService.LoadBalanceStats> getApiKeyStats(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            // 这里应该验证用户权限，简化处理
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(keyId);

            if (stats != null) {
                log.debug("获取API密钥负载统计成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("获取成功", stats);
            } else {
                return Result.errorTyped(404, "统计信息不存在");
            }
        } catch (Exception e) {
            log.error("获取API密钥负载统计失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    // ========================
    // 私有辅助方法
    // ========================

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户未认证");
        }

        try {
            CustomUserDetails userDetails=(CustomUserDetails)authentication.getPrincipal();
            return userDetails.getUserId();
        } catch (NumberFormatException e) {
            throw new RuntimeException("无效的用户ID");
        }
    }

    /**
     * 转换为用户配置DTO
     */
    private UserConfigDto convertToUserConfigDto(UserAiConfig config) {
        UserConfigDto dto = new UserConfigDto();
        dto.setPreferredModel(config.getPreferredModel());
        dto.setDefaultTemperature(config.getDefaultTemperature());
        dto.setDefaultMaxTokens(config.getDefaultMaxTokens());
        dto.setDefaultTopP(config.getDefaultTopP());
        dto.setStreamEnabled(config.getStreamEnabled());
        dto.setTimeoutSeconds(config.getTimeoutSeconds());
        dto.setSystemPrompt(config.getSystemPrompt());
        return dto;
    }

    /**
     * 转换为用户配置实体
     */
    private UserAiConfig convertToUserAiConfig(UserConfigDto dto) {
        UserAiConfig config = new UserAiConfig();
        config.setPreferredModel(dto.getPreferredModel());
        config.setDefaultTemperature(dto.getDefaultTemperature());
        config.setDefaultMaxTokens(dto.getDefaultMaxTokens());
        config.setDefaultTopP(dto.getDefaultTopP());
        config.setStreamEnabled(dto.getStreamEnabled());
        config.setTimeoutSeconds(dto.getTimeoutSeconds());
        config.setSystemPrompt(dto.getSystemPrompt());
        return config;
    }

    /**
     * 转换为API密钥DTO
     */
    private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
        ApiKeyDto dto = new ApiKeyDto();
        dto.setId(apiKey.getId());
        dto.setProvider(apiKey.getProvider());
        dto.setKeyName(apiKey.getKeyName());
        dto.setMaskedApiKey(apiKey.getApiKeyEncrypted()); // 已经脱敏
        dto.setIsActive(apiKey.getIsActive());
        dto.setPriority(apiKey.getPriority());
        dto.setUsageCount(apiKey.getUsageCount());
        dto.setLastUsedAt(apiKey.getLastUsedAt());
        dto.setCreatedAt(apiKey.getCreatedAt());

        // 获取负载统计信息
        try {
            LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(apiKey.getId());
            if (stats != null) {
                dto.setIsHealthy(stats.getIsHealthy());
                dto.setCurrentRequests(stats.getCurrentRequests());
                dto.setErrorCount(stats.getErrorCount());
                dto.setErrorRate(stats.getErrorRate());
            }
        } catch (Exception e) {
            log.warn("获取API密钥负载统计失败 - ID: {}", apiKey.getId(), e);
        }

        return dto;
    }

    /**
     * 生成OpenAI格式的兼容API密钥（使用随机盐加密并存储到数据库）
     */
    @PostMapping("/generate-compatible-key")
    @Operation(summary = "生成兼容API密钥", description = "为当前用户生成OpenAI格式的兼容API密钥")
    @ApiResponse(responseCode = "200", description = "生成成功")
    public Result<CompatibleKeyResult> generateCompatibleKey(
            @RequestParam(required = false, defaultValue = "default") String keyName,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);

            if (keyName == null || keyName.trim().isEmpty()) {
                keyName = "default";
            }

            // 1. 检查密钥名称是否已存在
            int existingCount = compatibleApiKeyMapper.countByUserIdAndKeyName(userId, keyName.trim());
            if (existingCount > 0) {
                return Result.errorTyped(400, "密钥名称已存在，请使用不同的名称");
            }

            // 2. 生成随机盐
            String randomSalt = KeyGenerators.string().generateKey();

            // 3. 构造明文数据：userId|timestamp|keyName|random
            String plaintext = String.format("%d|%d|%s|%d",
                userId,
                System.currentTimeMillis(),
                keyName.trim(),
                ThreadLocalRandom.current().nextLong()
            );

            // 4. 使用随机盐创建加密器并加密
            BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, randomSalt);
            byte[] encryptedBytes = encryptor.encrypt(plaintext.getBytes(StandardCharsets.UTF_8));
            String encrypted = Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedBytes);
            String compatibleKey = "sk-" + encrypted;

            // 5. 生成密钥哈希用于标识
            String keyHash = generateHash(compatibleKey);

            // 6. 存储到数据库
            CompatibleApiKey entity = new CompatibleApiKey();
            entity.setUserId(userId);
            entity.setKeyName(keyName.trim());
            entity.setKeyHash(keyHash);
            entity.setSalt(randomSalt);
            entity.setUsageCount(0L);
            entity.setCreatedAt(Instant.now());

            int insertResult = compatibleApiKeyMapper.insert(entity);
            if (insertResult <= 0) {
                return Result.errorTyped(500, "保存兼容密钥到数据库失败");
            }

            CompatibleKeyResult result = new CompatibleKeyResult(
                compatibleKey,
                "兼容API密钥已生成并保存，可用于所有AI服务调用",
                keyHash,
                entity.getId()
            );

            log.info("生成兼容API密钥成功 - 用户ID: {}, 密钥名称: {}, 密钥ID: {}", userId, keyName, entity.getId());
            return Result.success("生成成功", result);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            return Result.errorTyped(500, "生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的兼容API密钥列表
     */
    @GetMapping("/compatible-keys")
    @Operation(summary = "获取兼容API密钥列表", description = "获取当前用户的所有兼容API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<CompatibleKeyInfo>> getUserCompatibleKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<CompatibleApiKey> entities = compatibleApiKeyMapper.selectByUserId(userId);

            List<CompatibleKeyInfo> result = entities.stream()
                    .map(entity -> new CompatibleKeyInfo(
                        entity.getId(),
                        entity.getKeyName(),
                        maskKeyHash(entity.getKeyHash()),
                        entity.getUsageCount(),
                        entity.getLastUsedAt(),
                        entity.getCreatedAt(),
                        entity.getUpdatedAt()
                    ))
                    .collect(Collectors.toList());

            log.debug("获取用户兼容API密钥列表成功 - 用户ID: {}, 数量: {}", userId, result.size());
            return Result.success("获取成功", result);
        } catch (Exception e) {
            log.error("获取用户兼容API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 删除兼容API密钥
     */
    @DeleteMapping("/compatible-keys/{keyId}")
    @Operation(summary = "删除兼容API密钥", description = "删除指定的兼容API密钥")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteCompatibleKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleApiKey entity = compatibleApiKeyMapper.selectById(keyId);

            if (entity == null || !entity.getUserId().equals(userId)) {
                return Result.errorTyped(404, "兼容API密钥不存在或无权限删除");
            }

            int deleteResult = compatibleApiKeyMapper.deleteById(keyId);
            if (deleteResult <= 0) {
                return Result.errorTyped(500, "删除兼容API密钥失败");
            }

            log.info("删除兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
            return Result.success("删除成功", "删除成功");
        } catch (Exception e) {
            log.error("删除兼容API密钥失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证兼容API密钥（从数据库获取盐值进行解密验证）
     */
    @PostMapping("/validate-compatible-key")
    @Operation(summary = "验证兼容API密钥", description = "验证兼容API密钥的有效性")
    @ApiResponse(responseCode = "200", description = "验证完成")
    public Result<CompatibleKeyValidation> validateCompatibleKey(
            @RequestParam String compatibleKey,
            Authentication authentication) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return Result.success("验证完成", new CompatibleKeyValidation(false, null, "密钥格式无效"));
            }

            // 1. 生成密钥哈希
            String keyHash = generateHash(compatibleKey);

            // 2. 从数据库查找对应的盐值
            CompatibleApiKey entity = compatibleApiKeyMapper.selectByKeyHash(keyHash);
            if (entity == null) {
                return Result.success("验证完成", new CompatibleKeyValidation(false, null, "密钥不存在或已失效"));
            }

            // 3. 使用存储的盐值创建解密器
            String storedSalt = entity.getSalt();
            BytesEncryptor encryptor = Encryptors.stronger(encryptionPassword, storedSalt);

            // 4. 解密密钥内容
            String encrypted = compatibleKey.substring(3); // 去掉"sk-"
            byte[] encryptedBytes = Base64.getUrlDecoder().decode(encrypted);
            byte[] decryptedBytes = encryptor.decrypt(encryptedBytes);
            String plaintext = new String(decryptedBytes, StandardCharsets.UTF_8);
            String[] parts = plaintext.split("\\|");

            if (parts.length != 4) {
                return Result.success("验证完成", new CompatibleKeyValidation(false, null, "密钥数据格式无效"));
            }

            Long userId = Long.parseLong(parts[0]);
            Long timestamp = Long.parseLong(parts[1]);
            String keyName = parts[2];

            // 5. 验证时间戳（密钥有效期：1年）
            long currentTime = System.currentTimeMillis();
            if (currentTime - timestamp > 365L * 24 * 60 * 60 * 1000) {
                return Result.success("验证完成", new CompatibleKeyValidation(false, userId, "密钥已过期"));
            }

            // 6. 更新使用统计
            compatibleApiKeyMapper.updateUsageStatsByKeyHash(keyHash);

            CompatibleKeyValidation result = new CompatibleKeyValidation(true, userId, "验证成功");

            log.debug("验证兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return Result.success("验证完成", result);
        } catch (Exception e) {
            log.error("验证兼容API密钥失败", e);
            return Result.success("验证完成", new CompatibleKeyValidation(false, null, "验证失败: " + e.getMessage()));
        }
    }

    /**
     * 生成哈希值的辅助方法
     */
    private String generateHash(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
        } catch (Exception e) {
            throw new RuntimeException("生成哈希失败", e);
        }
    }

    /**
     * 脱敏显示密钥哈希
     */
    private String maskKeyHash(String keyHash) {
        if (keyHash == null || keyHash.length() < 8) {
            return "****";
        }
        return keyHash.substring(0, 4) + "****" + keyHash.substring(keyHash.length() - 4);
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final String compatibleKey;
        private final String message;
        private final String keyHash;
        private final Long keyId;

        public CompatibleKeyResult(String compatibleKey, String message, String keyHash, Long keyId) {
            this.compatibleKey = compatibleKey;
            this.message = message;
            this.keyHash = keyHash;
            this.keyId = keyId;
        }

        public String getCompatibleKey() { return compatibleKey; }
        public String getMessage() { return message; }
        public String getKeyHash() { return keyHash; }
        public Long getKeyId() { return keyId; }
    }

    /**
     * 兼容密钥验证结果
     */
    public static class CompatibleKeyValidation {
        private final boolean valid;
        private final Long userId;
        private final String message;

        public CompatibleKeyValidation(boolean valid, Long userId, String message) {
            this.valid = valid;
            this.userId = userId;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public String getMessage() { return message; }
    }

    /**
     * 兼容密钥信息（用于列表显示）
     */
    public static class CompatibleKeyInfo {
        private final Long id;
        private final String keyName;
        private final String maskedKeyHash;
        private final Long usageCount;
        private final Instant lastUsedAt;
        private final Instant createdAt;
        private final Instant updatedAt;

        public CompatibleKeyInfo(Long id, String keyName, String maskedKeyHash, Long usageCount,
                               Instant lastUsedAt, Instant createdAt, Instant updatedAt) {
            this.id = id;
            this.keyName = keyName;
            this.maskedKeyHash = maskedKeyHash;
            this.usageCount = usageCount;
            this.lastUsedAt = lastUsedAt;
            this.createdAt = createdAt;
            this.updatedAt = updatedAt;
        }

        public Long getId() { return id; }
        public String getKeyName() { return keyName; }
        public String getMaskedKeyHash() { return maskedKeyHash; }
        public Long getUsageCount() { return usageCount; }
        public Instant getLastUsedAt() { return lastUsedAt; }
        public Instant getCreatedAt() { return createdAt; }
        public Instant getUpdatedAt() { return updatedAt; }
    }
}
