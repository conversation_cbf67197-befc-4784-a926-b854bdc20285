package com.example.pure.controller;

import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.dto.response.Result;
import com.example.pure.service.CompatibleApiKeyService;
import com.example.pure.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 兼容API密钥控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/compatible-keys")
@RequiredArgsConstructor
@Validated
@Tag(name = "兼容API密钥管理", description = "兼容API密钥的创建、查询、删除等操作")
public class CompatibleApiKeyController {

    private final CompatibleApiKeyService compatibleApiKeyService;
    private final JwtUtil jwtUtil;

    @PostMapping
    @Operation(summary = "创建兼容API密钥", description = "为用户创建新的兼容API密钥")
    public Result<CompatibleApiKeyDto> createCompatibleKey(
            @Valid @RequestBody CreateCompatibleKeyRequest request,
            HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            CompatibleApiKeyDto result = compatibleApiKeyService.createCompatibleKey(userId, request);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建兼容API密钥失败", e);
            return Result.error("创建兼容API密钥失败: " + e.getMessage());
        }
    }

    @GetMapping
    @Operation(summary = "获取兼容API密钥列表", description = "获取当前用户的所有兼容API密钥")
    public Result<List<CompatibleApiKeyDto>> getUserCompatibleKeys(HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            List<CompatibleApiKeyDto> result = compatibleApiKeyService.getUserCompatibleKeys(userId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取兼容API密钥列表失败", e);
            return Result.error("获取兼容API密钥列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/{keyId}")
    @Operation(summary = "获取兼容API密钥详情", description = "根据ID获取兼容API密钥详情")
    public Result<CompatibleApiKeyDto> getCompatibleKeyById(
            @Parameter(description = "密钥ID") @PathVariable Long keyId,
            HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            CompatibleApiKeyDto result = compatibleApiKeyService.getCompatibleKeyById(userId, keyId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取兼容API密钥详情失败", e);
            return Result.error("获取兼容API密钥详情失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{keyId}")
    @Operation(summary = "删除兼容API密钥", description = "删除指定的兼容API密钥")
    public Result<Void> deleteCompatibleKey(
            @Parameter(description = "密钥ID") @PathVariable Long keyId,
            HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            compatibleApiKeyService.deleteCompatibleKey(userId, keyId);
            return Result.success();
        } catch (Exception e) {
            log.error("删除兼容API密钥失败", e);
            return Result.error("删除兼容API密钥失败: " + e.getMessage());
        }
    }

    @PostMapping("/validate")
    @Operation(summary = "验证兼容API密钥", description = "验证兼容API密钥是否有效")
    public Result<Boolean> validateCompatibleKey(
            @Parameter(description = "兼容API密钥") @RequestParam String compatibleKey) {
        try {
            boolean isValid = compatibleApiKeyService.validateCompatibleKey(compatibleKey);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证兼容API密钥失败", e);
            return Result.error("验证兼容API密钥失败: " + e.getMessage());
        }
    }
}
