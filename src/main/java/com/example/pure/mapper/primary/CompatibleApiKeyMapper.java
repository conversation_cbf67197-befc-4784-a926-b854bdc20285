package com.example.pure.mapper.primary;

import com.example.pure.model.entity.CompatibleApiKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 兼容API密钥数据访问层
 * <p>
 * 提供兼容API密钥的CRUD操作
 * </p>
 */
@Mapper
public interface CompatibleApiKeyMapper {

    /**
     * 根据ID查询兼容API密钥
     *
     * @param id 密钥ID
     * @return 兼容API密钥信息
     */
    CompatibleApiKey selectById(Long id);

    /**
     * 根据密钥哈希查询兼容API密钥
     *
     * @param keyHash 密钥哈希值
     * @return 兼容API密钥信息
     */
    CompatibleApiKey selectByKeyHash(String keyHash);

    /**
     * 根据用户ID查询所有兼容API密钥
     *
     * @param userId 用户ID
     * @return 兼容API密钥列表
     */
    List<CompatibleApiKey> selectByUserId(Long userId);

    /**
     * 插入兼容API密钥
     *
     * @param compatibleApiKey 兼容API密钥信息
     * @return 影响行数
     */
    int insert(CompatibleApiKey compatibleApiKey);

    /**
     * 更新兼容API密钥
     *
     * @param compatibleApiKey 兼容API密钥信息
     * @return 影响行数
     */
    int updateById(CompatibleApiKey compatibleApiKey);

    /**
     * 更新使用统计
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int updateUsageStats(Long id);

    /**
     * 根据密钥哈希更新使用统计
     *
     * @param keyHash 密钥哈希值
     * @return 影响行数
     */
    int updateUsageStatsByKeyHash(String keyHash);

    /**
     * 删除兼容API密钥
     *
     * @param id 密钥ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据用户ID删除所有兼容API密钥
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(Long userId);

    /**
     * 检查密钥名称是否已存在
     *
     * @param userId  用户ID
     * @param keyName 密钥名称
     * @return 存在的记录数
     */
    int countByUserIdAndKeyName(@Param("userId") Long userId, @Param("keyName") String keyName);
}
