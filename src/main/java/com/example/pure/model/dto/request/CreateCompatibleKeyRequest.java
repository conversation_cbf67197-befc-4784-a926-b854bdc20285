package com.example.pure.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 创建兼容API密钥请求DTO
 */
@Data
@Schema(description = "创建兼容API密钥请求")
public class CreateCompatibleKeyRequest {

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Size(min = 1, max = 100, message = "密钥名称长度必须在1-100个字符之间")
    @Schema(description = "密钥名称", required = true, example = "生产环境主密钥")
    private String keyName;
}
