package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * 兼容API密钥实体类
 * <p>
 * 存储兼容API密钥的元数据信息，包括随机盐和使用统计
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CompatibleApiKey {

    /**
     * 主键ID（数据库自增）
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 密钥名称（用户自定义）
     */
    private String keyName;

    /**
     * 密钥哈希值（用于标识兼容密钥）
     */
    private String keyHash;

    /**
     * 随机盐值（用于加密解密）
     */
    private String salt;

    /**
     * 使用次数统计
     */
    private Long usageCount;

    /**
     * 最后使用时间
     */
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;
}
