package com.example.pure.service;

import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;

import java.util.List;

/**
 * 兼容API密钥服务接口
 */
public interface CompatibleApiKeyService {

    /**
     * 创建兼容API密钥
     *
     * @param userId 用户ID
     * @param request 创建请求
     * @return 兼容API密钥信息
     */
    CompatibleApiKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request);

    /**
     * 获取用户的所有兼容API密钥
     *
     * @param userId 用户ID
     * @return 兼容API密钥列表
     */
    List<CompatibleApiKeyDto> getUserCompatibleKeys(Long userId);

    /**
     * 根据ID获取兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 密钥ID
     * @return 兼容API密钥信息
     */
    CompatibleApiKeyDto getCompatibleKeyById(Long userId, Long keyId);

    /**
     * 删除兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 密钥ID
     */
    void deleteCompatibleKey(Long userId, Long keyId);

    /**
     * 验证兼容API密钥
     *
     * @param compatibleKey 兼容密钥
     * @return 验证结果
     */
    boolean validateCompatibleKey(String compatibleKey);

    /**
     * 实体转DTO
     *
     * @param entity 实体对象
     * @param showFullKey 是否显示完整密钥
     * @return DTO对象
     */
    CompatibleApiKeyDto convertToDto(CompatibleApiKey entity, boolean showFullKey);
}
