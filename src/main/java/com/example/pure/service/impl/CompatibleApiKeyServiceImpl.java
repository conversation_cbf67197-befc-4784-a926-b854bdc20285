package com.example.pure.service.impl;

import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;
import com.example.pure.service.CompatibleApiKeyService;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 兼容API密钥服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompatibleApiKeyServiceImpl implements CompatibleApiKeyService {

    private final CompatibleApiKeyMapper compatibleApiKeyMapper;
    private final SpringEncryptionUtil springEncryptionUtil;

    @Override
    @Transactional
    public CompatibleApiKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request) {
        try {
            // 生成兼容API密钥（会自动存储到数据库）
            SpringEncryptionUtil.CompatibleKeyResult result = 
                springEncryptionUtil.generateCompatibleApiKey(userId, request.getKeyName());

            // 从数据库查询刚创建的记录
            CompatibleApiKey entity = compatibleApiKeyMapper.selectByKeyHash(result.getSecurityHash());
            if (entity == null) {
                throw new RuntimeException("创建兼容密钥后查询失败");
            }

            // 转换为DTO并返回完整密钥
            CompatibleApiKeyDto dto = convertToDto(entity, true);
            dto.setCompatibleKey(result.getCompatibleKey());

            log.info("创建兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, request.getKeyName());
            return dto;
        } catch (Exception e) {
            log.error("创建兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, request.getKeyName(), e);
            throw new RuntimeException("创建兼容API密钥失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CompatibleApiKeyDto> getUserCompatibleKeys(Long userId) {
        try {
            List<CompatibleApiKey> entities = compatibleApiKeyMapper.selectByUserId(userId);
            return entities.stream()
                    .map(entity -> convertToDto(entity, false)) // 不显示完整密钥
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户兼容API密钥列表失败 - 用户ID: {}", userId, e);
            throw new RuntimeException("获取兼容API密钥列表失败", e);
        }
    }

    @Override
    public CompatibleApiKeyDto getCompatibleKeyById(Long userId, Long keyId) {
        try {
            CompatibleApiKey entity = compatibleApiKeyMapper.selectById(keyId);
            if (entity == null || !entity.getUserId().equals(userId)) {
                throw new RuntimeException("兼容API密钥不存在或无权限访问");
            }
            return convertToDto(entity, false); // 不显示完整密钥
        } catch (Exception e) {
            log.error("获取兼容API密钥详情失败 - 用户ID: {}, 密钥ID: {}", userId, keyId, e);
            throw new RuntimeException("获取兼容API密钥详情失败", e);
        }
    }

    @Override
    @Transactional
    public void deleteCompatibleKey(Long userId, Long keyId) {
        try {
            CompatibleApiKey entity = compatibleApiKeyMapper.selectById(keyId);
            if (entity == null || !entity.getUserId().equals(userId)) {
                throw new RuntimeException("兼容API密钥不存在或无权限删除");
            }

            int deleteResult = compatibleApiKeyMapper.deleteById(keyId);
            if (deleteResult <= 0) {
                throw new RuntimeException("删除兼容API密钥失败");
            }

            log.info("删除兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
        } catch (Exception e) {
            log.error("删除兼容API密钥失败 - 用户ID: {}, 密钥ID: {}", userId, keyId, e);
            throw new RuntimeException("删除兼容API密钥失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateCompatibleKey(String compatibleKey) {
        try {
            SpringEncryptionUtil.ParseResultV2 result = 
                springEncryptionUtil.parseCompatibleApiKey(compatibleKey);
            return result.isValid();
        } catch (Exception e) {
            log.warn("验证兼容API密钥失败: {}", compatibleKey, e);
            return false;
        }
    }

    @Override
    public CompatibleApiKeyDto convertToDto(CompatibleApiKey entity, boolean showFullKey) {
        CompatibleApiKeyDto dto = new CompatibleApiKeyDto();
        dto.setId(entity.getId());
        dto.setKeyName(entity.getKeyName());
        dto.setKeyHash(entity.getKeyHash());
        dto.setUsageCount(entity.getUsageCount());
        dto.setLastUsedAt(entity.getLastUsedAt());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());

        // 根据参数决定是否显示完整密钥
        if (!showFullKey) {
            // 脱敏显示密钥哈希
            String maskedHash = entity.getKeyHash();
            if (maskedHash != null && maskedHash.length() > 8) {
                maskedHash = maskedHash.substring(0, 4) + "****" + maskedHash.substring(maskedHash.length() - 4);
            }
            dto.setCompatibleKey("sk-" + maskedHash);
        }

        return dto;
    }
}
