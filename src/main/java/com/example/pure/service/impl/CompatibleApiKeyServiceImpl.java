package com.example.pure.service.impl;

import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.CompatibleApiKeyService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 兼容API密钥服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompatibleApiKeyServiceImpl implements CompatibleApiKeyService {

    private final CompatibleApiKeyMapper compatibleApiKeyMapper;
    private final SpringEncryptionUtil springEncryptionUtil;
    private final LoadBalancerService loadBalancerService;

    @Override
    @Transactional
    public CompatibleApiKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request) {
        try {
            String keyName = request.getKeyName();
            if (keyName == null || keyName.trim().isEmpty()) {
                keyName = "default";
            }

            // 1. 检查密钥名称是否已存在
            int existingCount = compatibleApiKeyMapper.countByUserIdAndKeyName(userId, keyName.trim());
            if (existingCount > 0) {
                throw new IllegalArgumentException("密钥名称已存在，请使用不同的名称");
            }

            // 2. 使用Spring工具类生成兼容密钥和加密数据
            SpringEncryptionUtil.CompatibleKeyResult result =
                springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

            // 3. 存储到数据库
            CompatibleApiKey entity = new CompatibleApiKey();
            entity.setUserId(userId);
            entity.setKeyName(keyName.trim());
            entity.setKeyHash(result.getSecurityHash());
            entity.setSalt(result.getSalt()); // 存储Spring工具类返回的salt
            entity.setUsageCount(0L);
            entity.setCreatedAt(Instant.now());

            int insertResult = compatibleApiKeyMapper.insert(entity);
            if (insertResult <= 0) {
                throw new RuntimeException("保存兼容密钥到数据库失败");
            }

            // 4. 转换为DTO并返回完整密钥
            CompatibleApiKeyDto dto = convertToDto(entity, true);
            dto.setCompatibleKey(result.getCompatibleKey());

            log.info("创建兼容API密钥成功 - 用户ID: {}, 密钥名称: {}, 密钥ID: {}", userId, keyName, entity.getId());
            return dto;
        } catch (Exception e) {
            log.error("创建兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, request.getKeyName(), e);
            throw new RuntimeException("创建兼容API密钥失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CompatibleApiKeyDto> getUserCompatibleKeys(Long userId) {
        try {
            List<CompatibleApiKey> entities = compatibleApiKeyMapper.selectByUserId(userId);
            return entities.stream()
                    .map(entity -> convertToDto(entity, false)) // 不显示完整密钥
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户兼容API密钥列表失败 - 用户ID: {}", userId, e);
            throw new RuntimeException("获取兼容API密钥列表失败", e);
        }
    }

    @Override
    public CompatibleApiKeyDto getCompatibleKeyById(Long userId, Long keyId) {
        try {
            CompatibleApiKey entity = compatibleApiKeyMapper.selectById(keyId);
            if (entity == null || !entity.getUserId().equals(userId)) {
                throw new RuntimeException("兼容API密钥不存在或无权限访问");
            }
            return convertToDto(entity, false); // 不显示完整密钥
        } catch (Exception e) {
            log.error("获取兼容API密钥详情失败 - 用户ID: {}, 密钥ID: {}", userId, keyId, e);
            throw new RuntimeException("获取兼容API密钥详情失败", e);
        }
    }

    @Override
    @Transactional
    public void deleteCompatibleKey(Long userId, Long keyId) {
        try {
            CompatibleApiKey entity = compatibleApiKeyMapper.selectById(keyId);
            if (entity == null || !entity.getUserId().equals(userId)) {
                throw new RuntimeException("兼容API密钥不存在或无权限删除");
            }

            int deleteResult = compatibleApiKeyMapper.deleteById(keyId);
            if (deleteResult <= 0) {
                throw new RuntimeException("删除兼容API密钥失败");
            }

            log.info("删除兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
        } catch (Exception e) {
            log.error("删除兼容API密钥失败 - 用户ID: {}, 密钥ID: {}", userId, keyId, e);
            throw new RuntimeException("删除兼容API密钥失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateCompatibleKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return false;
            }

            // 1. 生成密钥哈希
            String keyHash = generateHash(compatibleKey);

            // 2. 从数据库查找对应的盐值
            CompatibleApiKey entity = compatibleApiKeyMapper.selectByKeyHash(keyHash);
            if (entity == null) {
                return false;
            }

            // 3. 使用Spring工具类和存储的盐值进行解密验证
            SpringEncryptionUtil.ParseResultV2 result =
                springEncryptionUtil.parseCompatibleApiKey(compatibleKey, entity.getSalt());

            if (result.isValid()) {
                // 4. 更新使用统计
                compatibleApiKeyMapper.updateUsageStatsByKeyHash(keyHash);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("验证兼容API密钥失败: {}", compatibleKey, e);
            return false;
        }
    }

    /**
     * 生成哈希值的辅助方法
     */
    private String generateHash(String data) {
        try {
            java.security.MessageDigest digest = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(data.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            return java.util.Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
        } catch (Exception e) {
            throw new RuntimeException("生成哈希失败", e);
        }
    }

    @Override
    public CompatibleApiKeyDto convertToDto(CompatibleApiKey entity, boolean showFullKey) {
        CompatibleApiKeyDto dto = new CompatibleApiKeyDto();
        dto.setId(entity.getId());
        dto.setKeyName(entity.getKeyName());
        dto.setKeyHash(entity.getKeyHash());
        dto.setUsageCount(entity.getUsageCount());
        dto.setLastUsedAt(entity.getLastUsedAt());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());

        // 根据参数决定是否显示完整密钥
        if (!showFullKey) {
            // 脱敏显示密钥哈希
            String maskedHash = entity.getKeyHash();
            if (maskedHash != null && maskedHash.length() > 8) {
                maskedHash = maskedHash.substring(0, 4) + "****" + maskedHash.substring(maskedHash.length() - 4);
            }
            dto.setCompatibleKey("sk-" + maskedHash);
        }

        return dto;
    }

    @Override
    public String generateSimpleCompatibleApiKey(Long userId, String keyName) {
        try {
            // 使用AES-GCM算法生成兼容密钥
            SpringEncryptionUtil.CompatibleKeyResult result =
                springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

            log.info("生成简单兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return result.getCompatibleKey();
        } catch (Exception e) {
            log.error("生成简单兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, keyName, e);
            throw new RuntimeException("生成简单兼容API密钥失败: " + e.getMessage());
        }
    }

    @Override
    public CompatibleKeyValidationResult validateCompatibleKeyWithUserId(String compatibleKey) {
        try {
            if (compatibleKey == null) {
                return new CompatibleKeyValidationResult(false, null, "兼容密钥不能为空");
            }

            if (!compatibleKey.startsWith("sk-")) {
                return new CompatibleKeyValidationResult(false, null, "不支持的密钥格式");
            }

            // 首先尝试使用新的兼容密钥服务进行验证
            boolean isValid = validateCompatibleKey(compatibleKey);
            if (isValid) {
                // 注意：由于安全考虑，新的验证方法不返回用户ID
                // 如果需要用户ID，应该通过其他方式获取
                log.debug("兼容密钥验证成功（新格式）");
                return new CompatibleKeyValidationResult(true, null, "验证成功");
            }

            // 如果新格式验证失败，尝试旧格式（简单格式）
            SpringEncryptionUtil.ParseResultV2 parseResult =
                springEncryptionUtil.parseCompatibleApiKey(compatibleKey);

            if (!parseResult.isValid()) {
                return new CompatibleKeyValidationResult(false, null, parseResult.getMessage());
            }

            Long userId = parseResult.getUserId();
            if (userId == null || userId <= 0) {
                return new CompatibleKeyValidationResult(false, null, "无效的用户ID");
            }

            log.debug("兼容密钥验证成功（简单格式） - 用户ID: {}", userId);
            return new CompatibleKeyValidationResult(true, userId, "验证成功");
        } catch (Exception e) {
            log.error("验证兼容密钥失败: {}", compatibleKey, e);
            return new CompatibleKeyValidationResult(false, null, "验证失败: " + e.getMessage());
        }
    }

    @Override
    public ApiKeySelectionResult selectApiKeyForModel(Long userId, String model) {
        try {
            // 根据模型确定提供商类型
            UserApiKey.ProviderType provider = determineProviderByModel(model);
            if (provider == null) {
                return new ApiKeySelectionResult(false, null, null,
                    "不支持的模型: " + model);
            }

            // 使用现有的负载均衡服务选择最佳API密钥
            UserApiKey selectedApiKey = loadBalancerService.selectBestApiKey(userId, provider);
            if (selectedApiKey == null) {
                return new ApiKeySelectionResult(false, null, null,
                    String.format("用户%d没有可用的%s提供商API密钥", userId, provider.getDisplayName()));
            }

            // 解密API密钥
            String decryptedKey = springEncryptionUtil.decrypt(selectedApiKey.getApiKeyEncrypted());

            log.info("为用户{}选择API密钥成功 - 密钥ID: {}, 提供商: {}, 模型: {}",
                    userId, selectedApiKey.getId(), provider, model);

            return new ApiKeySelectionResult(true, selectedApiKey, decryptedKey, "选择成功");
        } catch (Exception e) {
            log.error("选择API密钥失败 - 用户: {}, 模型: {}", userId, model, e);
            return new ApiKeySelectionResult(false, null, null, "选择失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型名称确定提供商类型
     * <p>
     * 决策理由：支持更多模型名称格式，包括最新的模型
     * </p>
     */
    private UserApiKey.ProviderType determineProviderByModel(String model) {
        if (model == null || model.trim().isEmpty()) {
            return null;
        }

        String modelLower = model.toLowerCase();

        // OpenAI 模型
        if (modelLower.startsWith("gpt-") ||
            modelLower.startsWith("text-") ||
            modelLower.startsWith("davinci") ||
            modelLower.startsWith("o1-") ||
            modelLower.startsWith("dall-e-") ||
            modelLower.equals("gpt-image-1")) {
            return UserApiKey.ProviderType.OPENAI;
        }

        // Anthropic 模型
        if (modelLower.startsWith("claude-") ||
            modelLower.contains("claude")) {
            return UserApiKey.ProviderType.ANTHROPIC;
        }

        // Google 模型
        if (modelLower.startsWith("gemini-") ||
            modelLower.contains("gemini") ||
            modelLower.startsWith("imagen-") ||
            modelLower.startsWith("palm-") ||
            modelLower.startsWith("bard-")) {
            return UserApiKey.ProviderType.GOOGLE;
        }

        log.warn("未知的模型类型: {}", model);
        return null;
    }
}
