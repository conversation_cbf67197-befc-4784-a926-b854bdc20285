package com.example.pure.service.openai.impl;

import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.CompatibleApiKeyService;
import com.example.pure.service.openai.SimpleCompatibleKeyService;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 简单兼容密钥服务实现类
 * <p>
 * 基于OpenAI格式的兼容密钥，通过解析密钥获取用户ID，
 * 然后使用现有的负载均衡服务选择最合适的原生API密钥
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleCompatibleKeyServiceImpl implements SimpleCompatibleKeyService {

    private final SpringEncryptionUtil springEncryptionUtil;
    private final LoadBalancerService loadBalancerService;

    @Override
    public String generateCompatibleApiKey(Long userId, String keyName) {
        try {
            // 使用AES-GCM算法生成兼容密钥
            SpringEncryptionUtil.CompatibleKeyResult result =
                springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

            log.info("生成兼容API密钥成功 - 用户ID: {}, 密钥名称: {}", userId, keyName);
            return result.getCompatibleKey();
        } catch (Exception e) {
            log.error("生成兼容API密钥失败 - 用户ID: {}, 密钥名称: {}", userId, keyName, e);
            throw new RuntimeException("生成兼容API密钥失败: " + e.getMessage());
        }
    }

    @Override
    public CompatibleKeyValidationResult validateCompatibleKey(String compatibleKey) {
        try {
            if (compatibleKey == null) {
                return new CompatibleKeyValidationResult(false, null, "兼容密钥不能为空");
            }

            if (!compatibleKey.startsWith("sk-")) {
                return new CompatibleKeyValidationResult(false, null, "不支持的密钥格式");
            }

            // 使用AES-GCM解析
            SpringEncryptionUtil.ParseResultV2 parseResult =
                springEncryptionUtil.parseCompatibleApiKey(compatibleKey);

            if (!parseResult.isValid()) {
                return new CompatibleKeyValidationResult(false, null, parseResult.getMessage());
            }

            Long userId = parseResult.getUserId();

            if (userId == null || userId <= 0) {
                return new CompatibleKeyValidationResult(false, null, "无效的用户ID");
            }

            log.debug("兼容密钥验证成功 - 用户ID: {}, 密钥名称: {}", userId, parseResult.getKeyName());
            return new CompatibleKeyValidationResult(true, userId, "验证成功");
        } catch (Exception e) {
            log.error("验证兼容密钥失败: {}", compatibleKey, e);
            return new CompatibleKeyValidationResult(false, null, "验证失败: " + e.getMessage());
        }
    }

    @Override
    public ApiKeySelectionResult selectApiKeyForModel(Long userId, String model) {
        try {


            // 根据模型确定提供商类型
            UserApiKey.ProviderType provider = determineProviderByModel(model);
            if (provider == null) {
                return new ApiKeySelectionResult(false, null, null,
                    "不支持的模型: " + model);
            }

            // 使用现有的负载均衡服务选择最佳API密钥
            UserApiKey selectedApiKey = loadBalancerService.selectBestApiKey(userId, provider);
            if (selectedApiKey == null) {
                return new ApiKeySelectionResult(false, null, null,
                    String.format("用户%d没有可用的%s提供商API密钥", userId, provider.getDisplayName()));
            }

            // 解密API密钥
            String decryptedKey = springEncryptionUtil.decrypt(selectedApiKey.getApiKeyEncrypted());

            log.info("为用户{}选择API密钥成功 - 密钥ID: {}, 提供商: {}, 模型: {}",
                    userId, selectedApiKey.getId(), provider, model);

            return new ApiKeySelectionResult(true, selectedApiKey, decryptedKey, "选择成功");
        } catch (Exception e) {
            log.error("选择API密钥失败 - 兼容用户: {}, 模型: {}",userId , model, e);
            return new ApiKeySelectionResult(false, null, null, "选择失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型名称确定提供商类型
     * <p>
     * 决策理由：支持更多模型名称格式，包括最新的模型
     * </p>
     */
    private UserApiKey.ProviderType determineProviderByModel(String model) {
        if (model == null || model.trim().isEmpty()) {
            return null;
        }

        String modelLower = model.toLowerCase();

        // OpenAI 模型
        if (modelLower.startsWith("gpt-") ||
            modelLower.startsWith("text-") ||
            modelLower.startsWith("davinci") ||
            modelLower.startsWith("o1-") ||
            modelLower.startsWith("dall-e-") ||
            modelLower.equals("gpt-image-1")) {
            return UserApiKey.ProviderType.OPENAI;
        }

        // Anthropic 模型
        if (modelLower.startsWith("claude-") ||
            modelLower.contains("claude")) {
            return UserApiKey.ProviderType.ANTHROPIC;
        }

        // Google 模型
        if (modelLower.startsWith("gemini-") ||
            modelLower.contains("gemini") ||
            modelLower.startsWith("imagen-") ||
            modelLower.startsWith("palm-") ||
            modelLower.startsWith("bard-")) {
            return UserApiKey.ProviderType.GOOGLE;
        }

        log.warn("未知的模型类型: {}", model);
        return null;
    }
}
