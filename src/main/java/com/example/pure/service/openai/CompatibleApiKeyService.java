package com.example.pure.service.openai;

import com.example.pure.model.dto.request.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.CompatibleApiKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;
import com.example.pure.model.entity.UserApiKey;

import java.util.List;

/**
 * 兼容API密钥服务接口
 */
public interface CompatibleApiKeyService {

    /**
     * 创建兼容API密钥
     *
     * @param userId 用户ID
     * @param request 创建请求
     * @return 兼容API密钥信息
     */
    CompatibleApiKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request);

    /**
     * 获取用户的所有兼容API密钥
     *
     * @param userId 用户ID
     * @return 兼容API密钥列表
     */
    List<CompatibleApiKeyDto> getUserCompatibleKeys(Long userId);

    /**
     * 根据ID获取兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 密钥ID
     * @return 兼容API密钥信息
     */
    CompatibleApiKeyDto getCompatibleKeyById(Long userId, Long keyId);

    /**
     * 删除兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 密钥ID
     */
    void deleteCompatibleKey(Long userId, Long keyId);

    /**
     * 验证兼容API密钥
     *
     * @param compatibleKey 兼容密钥
     * @return 验证结果
     */
    boolean validateCompatibleKey(String compatibleKey);

    /**
     * 实体转DTO
     *
     * @param entity 实体对象
     * @param showFullKey 是否显示完整密钥
     * @return DTO对象
     */
    CompatibleApiKeyDto convertToDto(CompatibleApiKey entity, boolean showFullKey);

    /**
     * 生成简单兼容API密钥（不存储到数据库）
     *
     * @param userId 用户ID
     * @param keyName 密钥名称
     * @return 兼容格式的API密钥
     */
    String generateSimpleCompatibleApiKey(Long userId, String keyName);

    /**
     * 验证兼容API密钥并获取用户ID（支持简单格式）
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 验证结果
     */
    CompatibleKeyValidationResult validateCompatibleKeyWithUserId(String compatibleKey);

    /**
     * 根据用户ID和模型选择最佳的原生API密钥
     *
     * @param userId 用户ID
     * @param model 请求的模型名称
     * @return API密钥选择结果
     */
    ApiKeySelectionResult selectApiKeyForModel(Long userId, String model);

    /**
     * 兼容密钥验证结果
     */
    class CompatibleKeyValidationResult {
        private final boolean valid;
        private final Long userId;
        private final String message;

        public CompatibleKeyValidationResult(boolean valid, Long userId, String message) {
            this.valid = valid;
            this.userId = userId;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public String getMessage() { return message; }
    }

    /**
     * API密钥选择结果
     */
    class ApiKeySelectionResult {
        private final boolean success;
        private final UserApiKey selectedApiKey;
        private final String decryptedKey;
        private final String message;

        public ApiKeySelectionResult(boolean success, UserApiKey selectedApiKey, String decryptedKey, String message) {
            this.success = success;
            this.selectedApiKey = selectedApiKey;
            this.decryptedKey = decryptedKey;
            this.message = message;
        }

        public boolean isSuccess() { return success; }
        public UserApiKey getSelectedApiKey() { return selectedApiKey; }
        public String getDecryptedKey() { return decryptedKey; }
        public String getMessage() { return message; }
    }
}
